<div class="editable-segment" data-section="parties" data-type="parties">
    <p>

        <strong>{!! $data['employer_name'] !!},</strong> {!! $data['employer_address'] !!}, {!! $data['employer_postal_code'] !!} {!! $data['employer_city'] !!}, {!! $data['employer_country'] !!}, OIB: {!! $data['employer_oib'] !!},

        @if(!empty($data['authorized_employer_persons']))
            @if(count($data['authorized_employer_persons']) == 1)
                kao poslodavac kojeg zastupa
            @else
                kao poslodavac kojeg zastupaju
            @endif

            @php // using <span></span> elements allows us to chain blade directives without spacing @endphp

            @foreach($data['authorized_employer_persons'] as $_i => $_authorized_person)
                    {!! $_authorized_person['role'] !!} {!! $_authorized_person['name']  !!}<span></span>@if($_authorized_person['has_entered_address']),
                    {!! !empty($_authorized_person['address']) ? $_authorized_person['address'] : $builder::$placeholder !!},
                    {!! !empty($_authorized_person['postal_code']) ? $_authorized_person['postal_code'] : $builder::$placeholder !!} {!! !empty($_authorized_person['city']) ? $_authorized_person['city'] : $builder::$placeholder  !!},
                    {!! !empty($_authorized_person['country']) ? $_authorized_person['country'] : $builder::$placeholder !!}@endif<span></span>@if(!empty($_authorized_person['oib'])), OIB: {!! $_authorized_person['oib'] !!}@endif<span></span>@if($_i < (count($data['authorized_employer_persons'])-1))@if(($_i+2) == count($data['authorized_employer_persons']))
                    i @else, @endif @else, @endif
            @endforeach
            <br><br>(u daljnjem tekstu: <strong>Poslodavac</strong>)
        @elseif($builder->partiesEmpty())
            kao poslodavac kojeg zastupa {!! $builder::$placeholder !!} {!! $builder::$placeholder !!},
            <br><br>(u daljnjem tekstu: <strong>Poslodavac</strong>)
        @else
            kao poslodavac <br><br>(u daljnjem tekstu: <strong>Poslodavac</strong>)
        @endif

    </p>

    <p>i</p>

    <p>

        <strong>{!! $data['employee_name'] !!},</strong> {!! $data['employee_address'] !!}, {!! $data['employee_postal_code'] !!} {!! $data['employee_city'] !!}, {!! $data['employee_country'] !!}, OIB: {!! $data['employee_oib'] !!},
        kao radnik <br><br>(u daljnjem tekstu: <strong>Radnik</strong>)
    </p>

    <p>
        (Poslodavac i Radnik u daljnjem tekstu zajednički: Ugovorne stranke)
    </p>

    <p>
        sklopili su sljedeći:
    </p>
</div>

<h2 class="text-center editable-segment" data-type="title">
    UGOVOR O RADU
    <br>
    @if($data['contract_type'] == "definite")
        na određeno vrijeme
    @else
        na neodređeno vrijeme
    @endif
</h2>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(1, true) }}. Predmet ugovora
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}. Ovim Ugovorom o radu (u daljnjem tekstu: Ugovor) zasniva se radni odnos Radnika kod Poslodavca (u daljnjem tekstu: Radni odnos) te se uređuju međusobna prava i obveze Ugovornih stranaka koja proizlaze iz tako zasnovanog Radnog odnosa i u vezi s njime.
        </p>
    </div>
</div>

@if($data['has_work_regulations'] || $data['has_collective_agreement'])
    <div class="editable-segment" data-type="article">
        <p class="article-header">
            Članak {{ $builder->getArticleIndex(2, true) }}.
            @if($data['has_work_regulations'] && $data['has_collective_agreement'])
                Pravilnik o radu i kolektivni ugovor
            @elseif($data['has_work_regulations'])
                Pravilnik o radu
            @elseif($data['has_collective_agreement'])
                Kolektivni ugovor
            @endif
        </p>
        <div class="article-body">
            @php $p_index = 1; @endphp
            @if($data['has_work_regulations'])
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}. Ugovorne stranke suglasno utvrđuju da se na Radni odnos primjenjuje Pravilnik o radu Poslodavca (u daljnjem tekstu: Pravilnik o radu). Radnik potpisom ovog Ugovora potvrđuje da mu je Poslodavac, u skladu s člankom 8. stavkom (3) trenutno važećeg Zakona o radu, stavio na raspolaganje primjerak Pravilnika o radu i svih drugih unutarnjih propisa Poslodavca na koje se Pravilnik o radu poziva, da ih je pročitao, da razumije sve njihove odredbe i da je s njima suglasan. Radnik prima na znanje i suglasan je s time da se Pravilnik o radu i svi drugi unutarnji propisi Poslodavca na koje se Pravilnik o radu poziva odlukom Poslodavca s vremena na vrijeme mogu mijenjati i dopunjavati u skladu s odredbama važećeg Zakona o radu. Radnik se obvezuje poštivati Pravilnik o radu i sve druge unutarnje propise Poslodavca na koje se Pravilnik o radu poziva.
                </p>
            @endif
            @if($data['has_collective_agreement'])
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}. Ugovorne stranke suglasno utvrđuju da se na Radni odnos primjenjuje {!! $data['collective_agreement_name'] !!} (u daljnjem tekstu: Kolektivni ugovor). Radnik potpisom ovog Ugovora potvrđuje da mu je Poslodavac, u skladu s člankom 8. stavkom (3) trenutno važećeg Zakona o radu, stavio na raspolaganje primjerak Kolektivnog ugovora.
                </p>
            @endif

            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}. Ako odredbe ovog Ugovora upućuju na primjenu pojedinih odredbi
                @if($data['has_work_regulations'] && $data['has_collective_agreement'])
                    Pravilnika o radu odnosno Kolektivnog ugovora,
                @elseif($data['has_work_regulations'])
                    Pravilnika o radu,
                @elseif($data['has_collective_agreement'])
                    Kolektivnog ugovora,
                @endif
                takve odredbe
                @if($data['has_work_regulations'] && $data['has_collective_agreement'])
                    Pravilnika o radu odnosno Kolektivnog ugovora
                @elseif($data['has_work_regulations'])
                    Pravilnika o radu
                @elseif($data['has_collective_agreement'])
                    Kolektivnog ugovora
                @endif
                čine sastavni dio ovog Ugovora.
            </p>

            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}. Ako su pojedina pitanja
                @if($data['has_work_regulations'] && $data['has_collective_agreement'])
                    Pravilnikom o radu odnosno Kolektivnim ugovorom
                @elseif($data['has_work_regulations'])
                    Pravilnikom o radu
                @elseif($data['has_collective_agreement'])
                    Kolektivnim ugovorom
                @endif
                uređena drukčije negoli u ovom Ugovoru, primjenjuju se odredbe ovog Ugovora pod uvjetom da to nije u suprotnosti s člankom 9. stavkom (3) trenutno važećeg Zakona o radu o primjeni za Radnika najpovoljnijeg prava.
            </p>
        </div>
    </div>
@else
    @php $builder->skipped_articles[] = 2; @endphp
@endif

<div class="editable-segment" data-section="type" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(3, true) }}. Trajanje ugovora i dan početka rada
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        @if($data['contract_type'] == "definite")
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}. Ovaj Ugovor sklapa se na određeno vrijeme
                @if($data['expected_duration'] == 'custom')
                    čije je očekivano trajanje {!! rtrim($data['expected_duration_custom'], '.') !!}.
                @else
                    do {!! $data['expected_duration_until_date'] !!} godine.
                @endif
            </p>

            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}. Ugovorne stranke suglasno utvrđuju da je objektivan razlog za sklapanje ugovora o radu na određeno vrijeme
                {!! $data['definite_contract_reason'] !!}.
            </p>
        @else
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Ovaj Ugovor sklapa se na neodređeno vrijeme.
            </p>
        @endif

        @if($data['work_start_date'] == 'custom')
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Radnik će započeti rad kod Poslodavca na temelju ovog Ugovora dana {!! $data['work_start_date_custom'] !!} godine.
            </p>
        @else
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Radnik će započeti rad kod Poslodavca na temelju ovog Ugovora dana {!! $data['contract_date'] !!} godine.
            </p>
        @endif
    </div>
</div>

<div class="editable-segment" data-section="general_provisions" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(4, true) }}. Mjesto rada
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        @if($data['work_location_type'] == 'flexible')
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Radnik se obvezuje poslove iz članka <span class="article-reference">{{ $builder->getArticleIndex(5) }}</span>. ovog Ugovora obavljati na različitim mjestima i to
                {!! $data['work_locations'] !!}, s time da se Radnik obvezuje te poslove privremeno obavljati i u nekom drugom mjestu u slučaju više sile, nastupanja drugih izvanrednih okolnosti ili prijeke potrebe organizacije rada, na temelju naloga Poslodavca.
            </p>
        @else
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Radnik se obvezuje poslove iz članka <span class="article-reference">{{ $builder->getArticleIndex(5) }}</span>. ovog Ugovora obavljati {!! $data['work_location'] !!}, kao stalnom (glavnom) mjestu rada, s time da se Radnik obvezuje te poslove
                privremeno obavljati i u nekom drugom mjestu u slučaju više sile, nastupanja drugih izvanrednih okolnosti ili prijeke potrebe organizacije rada, na temelju naloga Poslodavca.
            </p>
        @endif
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(5, true) }}. Radno mjesto i poslovi koje će obavljati Radnik
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Radnik se zapošljava na radnom mjestu <i>{!! $data['work_title'] !!}</i> (u daljnjem tekstu: Radno mjesto).
        </p>

        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Poslovi Radnog mjesta koje se Radnik obvezuje obavljati za Poslodavca, između ostalog, uključuju sljedeće:
        </p>
        <ul>
            @if(!empty($data['work_responsibilities']))
                @foreach($data['work_responsibilities'] as $_w_r_i => $_work_responsibility)
                    <li>{!! !empty($_work_responsibility) ? $_w_r_i !== (count($data['work_responsibilities']) - 1) ? $_work_responsibility : ($_work_responsibility.".") : $builder::$placeholder !!}</li>
                @endforeach
            @else
                <li>{!! $builder::$placeholder !!}</li>
                <li>{!! $builder::$placeholder !!}</li>
                <li>{!! $builder::$placeholder !!}</li>
            @endif
        </ul>

        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Osim poslova navedenih u prethodnom stavku, Radnik se obvezuje za Poslodavca obavljati i sve druge poslove koji po prirodi Radnog mjesta
            i pravilima struke spadaju u djelokrug rada Radnika.
        </p>

        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Radnik se obvezuje u slučaju više sile, nastupanja drugih izvanrednih okolnosti ili prijeke potrebe organizacije rada na temelju naloga Poslodavca
            privremeno obavljati i poslove nekog drugog radnog mjesta, u skladu sa svojim znanjima i sposobnostima.
        </p>
    </div>
</div>

@if($data['probation_period'])
    <div class="editable-segment" data-type="article">
        <p class="article-header">
            Članak {{ $builder->getArticleIndex(6, true) }}. Probni rad
        </p>
        <div class="article-body">
            @php $p_index = 1; @endphp
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Ugovorne stranke ugovaraju probni rad u svrhu provjere ima li Radnik osobine, znanja i sposobnosti potrebne za obavljanje poslova Radnog mjesta iz članka {{ $builder->getArticleIndex(5) }}. ovog Ugovora, odnosno udovoljava li Radnik zahtjevima Radnog mjesta (u daljnjem tekstu: Probni rad).
            </p>
            @if($data['has_work_regulations'] && $data['probation_period_type'] === 'defined_in_work_regulations')
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Na sva pitanja vezana za trajanje i uvjete Probnog rada primjenjuju se odgovarajuće odredbe Pravilnika o radu Poslodavca.
                </p>
            @elseif($data['has_collective_agreement'] && $data['probation_period_type'] === 'defined_in_collective_agreement')
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Na sva pitanja vezana za trajanje i uvjete Probnog rada primjenjuju se odgovarajuće odredbe Kolektivnog ugovora.
                </p>
            @else
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Probni rad ugovara se u trajanju od
                    {!! $data['probation_period_custom'] !!}.
                    Ako je tijekom trajanja Probnog rada Radnik bio privremeno odsutan, osobito zbog privremene nesposobnosti za rad, korištenja rodiljnih i roditeljskih prava prema posebnom propisu
                    i korištenja prava na plaćeni dopust iz članka 86. trenutno važećeg Zakona o radu, trajanje Probnog rada produljuje se razmjerno duljini trajanja nenazočnosti na Probnom radu, tako da ukupno trajanje Probnog rada prije i nakon njegovog prekida ne može biti dulje od
                    {!! $data['probation_period_custom'] !!}.
                </p>
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Poslodavac se obvezuje obavijestiti Radnika o njegovoj uspješnosti tijekom Probnog rada najkasnije zadnjeg dana Probnog rada. Ako Poslodavac tijekom trajanja Probnog rada, a najkasnije zadnjeg dana Probnog rada ne donese odluku o otkazu ovog Ugovora zbog nezadovoljavanja Radnika na Probnom radu, smatra se da je Radnik zadovoljio na Probnom radu te ovaj Ugovor ostaje u cijelosti na snazi.
                </p>
            @endif
        </div>
    </div>
@else
    @php $builder->skipped_articles[] = 6; @endphp
@endif

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(7, true) }}. Trajanje redovitog radnog tjedna i radno vrijeme
    </p>

    <div class="article-body">
        @php $p_index = 1; @endphp
        @if(isset($data['is_full_time']) && !$data['is_full_time'])
            @if($data['week_hours'] == 'custom')
                @if($data['working_hours'])
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Radnik će raditi u nepunom radnom vremenu u trajanju od {!! $data['week_hours_custom'] !!} {!! StringHelper::getSatiString($data['week_hours_custom']) !!} tjedno,
                        @switch($data['working_hours'])
                            @case('mon_fri_8_16')
                                od ponedjeljka do petka, od 8 do 16 sati
                                @break
                            @case('mon_fri_9_17')
                                od ponedjeljka do petka, od 9 do 17 sati
                                @break
                            @case('custom')
                                od {!! $data['working_hours_custom_start_day'] !!} do {!! $data['working_hours_custom_end_day'] !!},
                                s početkom u {!! $data['working_hours_custom_start_hour'] !!}
                                i završetkom u {!! $data['working_hours_custom_end_hour'] !!} {!! StringHelper::getSatiString($data['working_hours_custom_end_hour']) !!}
                        @endswitch
                        ili prema drukčijem rasporedu radnog vremena kojeg, u skladu s važećim Zakonom o radu, odredi Poslodavac.
                    </p>
                @else
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Radnik će raditi u nepunom radnom vremenu u trajanju od {!! $data['week_hours_custom'] !!} {!! StringHelper::getSatiString($data['week_hours_custom']) !!} tjedno,
                        prema rasporedu radnog vremena koji, u skladu s važećim Zakonom o radu, odredi Poslodavac.
                    </p>
                @endif
            @endif
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                U slučaju više sile, izvanrednog povećanja opsega poslova i u drugim sličnim slučajevima prijeke potrebe, Radnik
                je na zahtjev Poslodavca dužan raditi dulje od nepunog radnog vremena (u daljnjem tekstu: Prekovremeni rad).
            </p>
        @else
            @if($data['has_work_regulations'] && (!isset($data['week_hours']) || $data['week_hours'] === 'defined_in_work_regulations' || ($data['week_hours'] === 'defined_in_collective_agreement' && !$data['has_collective_agreement'])))
                @if($data['working_hours'])
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Radnik će raditi u punom radnom vremenu u trajanju određenom Pravilnikom o radu Poslodavca,
                        @switch($data['working_hours'])
                            @case('mon_fri_8_16')
                                od ponedjeljka do petka, od 8 do 16 sati
                                @break
                            @case('mon_fri_9_17')
                                od ponedjeljka do petka, od 9 do 17 sati
                                @break
                            @case('custom')
                                od {!! $data['working_hours_custom_start_day'] !!} do {!! $data['working_hours_custom_end_day'] !!},
                                s početkom u {!! $data['working_hours_custom_start_hour'] !!}
                                i završetkom u {!! $data['working_hours_custom_end_hour'] !!} {!! StringHelper::getSatiString($data['working_hours_custom_end_hour']) !!}
                        @endswitch
                        ili prema drukčijem rasporedu radnog vremena koji, u skladu s važećim Zakonom o radu, odredi
                        Poslodavac.
                    </p>
                @else
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Radnik će raditi u punom radnom vremenu u trajanju određenom Pravilnikom o radu Poslodavca,
                        prema rasporedu radnog vremena koji, u skladu s važećim Zakonom o radu, odredi
                        Poslodavac.
                    </p>
                @endif
            @elseif($data['has_collective_agreement'] && (!isset($data['week_hours']) || $data['week_hours'] === 'defined_in_collective_agreement' || ($data['week_hours'] === 'defined_in_work_regulations' && !$data['has_work_regulations'])))
                @if($data['working_hours'])
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Radnik će raditi u punom radnom vremenu u trajanju određenom Kolektivnim ugovorom,
                        @switch($data['working_hours'])
                            @case('mon_fri_8_16')
                                od ponedjeljka do petka, od 8 do 16 sati
                                @break
                            @case('mon_fri_9_17')
                                od ponedjeljka do petka, od 9 do 17 sati
                                @break
                            @case('custom')
                                od {!! $data['working_hours_custom_start_day'] !!} do {!! $data['working_hours_custom_end_day'] !!},
                                s početkom u {!! $data['working_hours_custom_start_hour'] !!}
                                i završetkom u {!! $data['working_hours_custom_end_hour'] !!} {!! StringHelper::getSatiString($data['working_hours_custom_end_hour']) !!}
                        @endswitch
                        ili prema drukčijem rasporedu radnog vremena koji, u skladu s važećim Zakonom o radu, odredi
                        Poslodavac.
                    </p>
                @else
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Radnik će raditi u punom radnom vremenu u trajanju određenom Kolektivnim ugovorom,
                        prema rasporedu radnog vremena koji, u skladu s važećim Zakonom o radu, odredi
                        Poslodavac.
                    </p>
                @endif
            @else
                @if($data['working_hours'])
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Radnik će raditi u punom radnom vremenu u trajanju od {!! $data['week_hours_custom'] !!} {!! StringHelper::getSatiString($data['week_hours_custom']) !!} tjedno,
                        @switch($data['working_hours'])
                            @case('mon_fri_8_16')
                                od ponedjeljka do petka, od 8 do 16 sati
                                @break
                            @case('mon_fri_9_17')
                                od ponedjeljka do petka, od 9 do 17 sati
                                @break
                            @case('custom')
                                od {!! $data['working_hours_custom_start_day'] !!} do {!! $data['working_hours_custom_end_day'] !!},
                                s početkom u {!! $data['working_hours_custom_start_hour'] !!}
                                i završetkom u {!! $data['working_hours_custom_end_hour'] !!} {!! StringHelper::getSatiString($data['working_hours_custom_end_hour']) !!}
                        @endswitch
                        ili prema drukčijem rasporedu radnog vremena kojeg, u skladu s važećim Zakonom o radu, odredi
                        Poslodavac.
                    </p>
                @else
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Radnik će raditi u punom radnom vremenu u trajanju od {!! $data['week_hours_custom'] !!} {!! StringHelper::getSatiString($data['week_hours_custom']) !!} tjedno,
                        prema rasporedu radnog vremena kojeg, u skladu s važećim Zakonom o radu, odredi Poslodavac.
                    </p>
                @endif
            @endif
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                U slučaju više sile, izvanrednog povećanja opsega poslova i u drugim sličnim slučajevima prijeke potrebe, Radnik
                je na zahtjev Poslodavca dužan raditi dulje od punog radnog vremena (u daljnjem tekstu: Prekovremeni rad).
            </p>
        @endif
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(8, true) }}. Godišnji odmor
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        @switch($data['paid_holidays_duration'])
            @case('minimum')
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Radnik za svaku kalendarsku godinu za vrijeme trajanja ovog Ugovora ima pravo na plaćeni godišnji odmor u
                    najmanjem trajanju propisanom važećim Zakonom o radu. Godišnji odmor Radniku se utvrđuje brojem radnih dana
                    ovisno o Radnikovom tjednom rasporedu radnog vremena. Blagdani i neradni dani određeni zakonom, razdoblje
                    privremene nesposobnosti za rad koje je utvrdio ovlašteni liječnik te dani plaćenog dopusta ne uračunavaju se u
                    trajanje godišnjeg odmora.
                </p>
                @break
            @case('defined_in_work_regulations')
                @if($data['has_work_regulations'])
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Na sva pitanja vezana za godišnji odmor Radnika primjenjuju se odgovarajuće odredbe Pravilnika o radu Poslodavca.
                    </p>
                @else
                    @if($data['has_collective_agreement'])
                        <p>
                            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                            Na sva pitanja vezana za godišnji odmor Radnika primjenjuju se odgovarajuće odredbe Kolektivnog ugovora.
                        </p>
                    @else
                        <p>
                            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                            Radnik za svaku kalendarsku godinu za vrijeme trajanja ovog Ugovora ima pravo na plaćeni godišnji odmor u
                            najmanjem trajanju propisanom važećim Zakonom o radu. Godišnji odmor Radniku se utvrđuje brojem radnih dana
                            ovisno o Radnikovom tjednom rasporedu radnog vremena. Blagdani i neradni dani određeni zakonom, razdoblje
                            privremene nesposobnosti za rad koje je utvrdio ovlašteni liječnik te dani plaćenog dopusta ne uračunavaju se u
                            trajanje godišnjeg odmora.
                        </p>
                    @endif
                @endif
                @break
            @case('defined_in_collective_agreement')
                @if($data['has_collective_agreement'])
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Na sva pitanja vezana za godišnji odmor Radnika primjenjuju se odgovarajuće odredbe Kolektivnog ugovora.
                    </p>
                @else
                    @if($data['has_work_regulations'])
                        <p>
                            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                            Na sva pitanja vezana za godišnji odmor Radnika primjenjuju se odgovarajuće odredbe Pravilnika o radu Poslodavca.
                        </p>
                    @else
                        <p>
                            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                            Radnik za svaku kalendarsku godinu za vrijeme trajanja ovog Ugovora ima pravo na plaćeni godišnji odmor u
                            najmanjem trajanju propisanom važećim Zakonom o radu. Godišnji odmor Radniku se utvrđuje brojem radnih dana
                            ovisno o Radnikovom tjednom rasporedu radnog vremena. Blagdani i neradni dani određeni zakonom, razdoblje
                            privremene nesposobnosti za rad koje je utvrdio ovlašteni liječnik te dani plaćenog dopusta ne uračunavaju se u
                            trajanje godišnjeg odmora.
                        </p>
                    @endif
                @endif
                @break
            @case('custom')
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Radnik za svaku kalendarsku godinu za vrijeme trajanja ovog Ugovora ima pravo na plaćeni godišnji odmor u
                    trajanju od {!! $data['paid_holidays_duration_custom'] !!}. Godišnji odmor Radniku se utvrđuje brojem radnih
                    dana ovisno o Radnikovom tjednom rasporedu radnog vremena. Blagdani i neradni dani određeni zakonom, razdoblje
                    privremene nesposobnosti za rad koje je utvrdio ovlašteni liječnik te dani plaćenog dopusta ne uračunavaju se u
                    trajanje godišnjeg odmora.
                </p>
                @break
            @default
                @if($data['has_work_regulations'])
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Na sva pitanja vezana za godišnji odmor Radnika primjenjuju se odgovarajuće odredbe Pravilnika o radu Poslodavca.
                    </p>
                @elseif($data['has_collective_agreement'])
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Na sva pitanja vezana za godišnji odmor Radnika primjenjuju se odgovarajuće odredbe Kolektivnog ugovora.
                    </p>
                @else
                    <p>
                        {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                        Radnik za svaku kalendarsku godinu za vrijeme trajanja ovog Ugovora ima pravo na plaćeni godišnji odmor u
                        najmanjem trajanju propisanom važećim Zakonom o radu. Godišnji odmor Radniku se utvrđuje brojem radnih dana
                        ovisno o Radnikovom tjednom rasporedu radnog vremena. Blagdani i neradni dani određeni zakonom, razdoblje
                        privremene nesposobnosti za rad koje je utvrdio ovlašteni liječnik te dani plaćenog dopusta ne uračunavaju se u
                        trajanje godišnjeg odmora.
                    </p>
                @endif
        @endswitch
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(9, true) }}. Plaća, naknada plaće i drugi primici radnika
    </p>
    <div class="article-body">

        @php $p_index = 1; @endphp

        @if($data['has_work_regulations'] && (!isset($data['compensation_management_type']) || $data['compensation_management_type'] === 'defined_in_work_regulations' || ($data['compensation_management_type'] === 'defined_in_collective_agreement' && !$data['has_collective_agreement'])))
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Na sva pitanja vezana za isplatu i razdoblja isplate osnovne plaće, dodataka na plaću i ostalih primitaka za obavljeni rad, kao i primitaka na temelju radnog odnosa na koje Radnik ima pravo, primjenjuju se odgovarajuće odredbe Pravilnika o radu Poslodavca.
            </p>
        @elseif($data['has_collective_agreement'] && (!isset($data['compensation_management_type']) || $data['compensation_management_type'] === 'defined_in_collective_agreement' || ($data['compensation_management_type'] === 'defined_in_work_regulations' && !$data['has_work_regulations'])))
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Na sva pitanja vezana za isplatu i razdoblja isplate osnovne plaće, dodataka na plaću i ostalih primitaka za obavljeni rad, kao i primitaka na temelju radnog odnosa na koje Radnik ima pravo, primjenjuju se odgovarajuće odredbe Kolektivnog ugovora.
            </p>
        @else
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Poslodavac se Radniku obvezuje isplaćivati osnovnu plaću u iznosu
                od {!! StringHelper::amountToText($data['gross_monthly_salary_custom']) !!} bruto za obavljeni rad u određenom mjesecu
                uz ostvareni uobičajeni radni učinak.
            </p>

            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Poslodavac se obvezuje Radniku isplaćivati dodatke odnosno povećanu plaću kako slijedi:
            </p>

            <ul>
                <li>za Prekovremeni rad osnovna plaća povećava se za {!! !empty($data['salary_bonuses']['overtime']) ? $data['salary_bonuses']['overtime'] : $builder::$placeholder !!} %</li>
                <li>za rad noću osnovna plaća povećava se za {!! !empty($data['salary_bonuses']['night_shift']) ? $data['salary_bonuses']['night_shift'] : $builder::$placeholder !!} %</li>
                <li>za rad u otežanim uvjetima rada osnovna plaća povećava se za {!! !empty($data['salary_bonuses']['hard_conditions']) ? $data['salary_bonuses']['hard_conditions'] : $builder::$placeholder !!} %</li>
                <li>za rad nedjeljom osnovna plaća povećava se za {!! !empty($data['salary_bonuses']['sunday']) ? $data['salary_bonuses']['sunday'] : $builder::$placeholder !!} %</li>
                <li>za rad blagdanom ili nekim drugim danom za koji je zakonom određeno da se ne radi osnovna plaća povećava se za {!! !empty($data['salary_bonuses']['holidays']) ? $data['salary_bonuses']['holidays'] : $builder::$placeholder !!} %<span></span>@if(empty($data['custom_salary_bonuses'])). @else @endif</li>

                @if(!empty($data['custom_salary_bonuses']))
                    @foreach($data['custom_salary_bonuses'] as $_c_s_b_i => $_custom_salary_bonus)
                        <li>{!! $_custom_salary_bonus['name'] ?: $builder::$placeholder !!} osnovna plaća povećava se za
                            {!! $_custom_salary_bonus['type'] === 'eur' ? (!empty($_custom_salary_bonus['value']) ? (StringHelper::amountToText($_custom_salary_bonus['value']) . (($_c_s_b_i === count($data['custom_salary_bonuses'])-1) ? "." : null)) : $builder::$placeholder) :  (!empty($_custom_salary_bonus['value']) ? ($_custom_salary_bonus['value'] . " %" . (($_c_s_b_i === count($data['custom_salary_bonuses'])-1) ? "." : " ")) : $builder::$placeholder) !!}</li>
                    @endforeach
                @endif
            </ul>

            @if($data['salary_perks'] || $data['custom_salary_perks'])
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Poslodavac se obvezuje Radniku isplaćivati
                    {!! ((is_array($data['salary_perks']) ? count($data['salary_perks']) : 0) + (is_array($data['custom_salary_perks']) ? count($data['custom_salary_perks']) : 0)) > 1 ? "primitke" : "primitak" !!}
                    kao dio plaće za obavljeni rad u određenom mjesecu kako slijedi:
                </p>
                <ul>
                    @if(!empty($data['salary_perks']) && in_array('car', $data['salary_perks']))
                        <li>
                            primitak u naravi po osnovi korištenja u privatne svrhe Radnika službenog automobila marke
                            {!! $data['salary_perk_car_model'] !!},
                            registarske oznake {!! $data['salary_perk_car_licence_plates'] !!},
                            broja šasije {!! $data['salary_perk_car_identification_number'] !!},
                            čiji bruto iznos odgovara iznosu {!! $data['salary_perk_car_amount'] ?? '1 % od nabavne vrijednosti vozila (uvećano za PDV)' !!}
                            uvećanom za doprinose, porez na dohodak i prirez porezu na dohodak<span></span>@if(empty($data['custom_salary_perks'])). @endif
                        </li>
                    @endif

                    @if(!empty($data['custom_salary_perks']))
                        @foreach($data['custom_salary_perks'] as $_c_s_p_i => $_custom_salary_perk)
                            <li>{!! $_custom_salary_perk['name'] ?: $builder::$placeholder !!} čiji bruto iznos je
                                {!! $_custom_salary_perk['type'] === 'eur'
                                    ? (!empty($_custom_salary_perk['value']) ? (StringHelper::amountToText($_custom_salary_perk['value']) . ($_c_s_p_i === count($data['custom_salary_perks']) - 1 ? "." : null)) : $builder::$placeholder)
                                    : (!empty($_custom_salary_perk['value']) ? ($_custom_salary_perk['value'] . ($_c_s_p_i === count($data['custom_salary_perks']) - 1 ? "." : null)) : $builder::$placeholder)
                                !!}
                            </li>
                        @endforeach
                    @endif
                </ul>
            @endif

            @php $contains_monthly_custom_material_perk = false; @endphp
            @php $contains_non_monthly_custom_material_perk = false; @endphp
            @if($data['material_perks'] || $data['custom_material_perks'])

                @php $custom_material_perk_article_index = $builder->getCurrentArticleIndex().".$p_index."; @endphp
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Poslodavac se obvezuje Radniku isplaćivati
                    {!! ((is_array($data['material_perks']) ? count($data['material_perks']) : 0) + (is_array($data['custom_material_perks']) ? count($data['custom_material_perks']) : 0)) > 1 ? "primitke" : "primitak" !!}
                    na temelju radnog odnosa kako slijedi:
                </p>
                <ul>
                    @if(!empty($data['material_perks']))
                        @foreach($data['material_perks'] as $_m_p_i => $_material_perk)
                            @if($_material_perk == 'christmas')
                                <li>božićnicu u iznosu od {!! StringHelper::amountToText($data['material_perk_christmas']) !!} bruto (godišnja isplata)<span></span>@if($_m_p_i === count($data['material_perks']) - 1 && empty($data['custom_material_perks'])). @endif</li>
                            @endif
                            @if($_material_perk == 'easter')
                                <li>uskrsnicu u iznosu od {!! StringHelper::amountToText($data['material_perk_easter']) !!} bruto (godišnja isplata)<span></span>@if($_m_p_i === count($data['material_perks']) - 1 && empty($data['custom_material_perks'])). @endif</li>
                            @endif
                            @if($_material_perk == 'child_gift')
                                <li>
                                    dar za djecu (po djetetu do 15 godina starosti) u iznosu od {!! StringHelper::amountToText($data['material_perk_child_gift']) !!} bruto
                                    (godišnja isplata)<span></span>@if($_m_p_i === count($data['material_perks']) - 1 && empty($data['custom_material_perks'])). @endif
                                </li>
                            @endif
                            @if($_material_perk == 'vacation_subvention')
                                <li>
                                    naknadu za godišnji odmor (regres) u
                                    iznosu od {!! StringHelper::amountToText($data['material_perk_vacation_subvention']) !!} bruto (godišnja isplata)<span></span>@if($_m_p_i === count($data['material_perks']) - 1 && empty($data['custom_material_perks'])). @endif
                                </li>
                            @endif
                            @if($_material_perk == 'meal_subvention')
                                <li>
                                    novčanu paušalnu naknadu za podmirivanje troškova prehrane radnika
                                    u iznosu od {!! StringHelper::amountToText($data['material_perk_meal_subvention']) !!} bruto (mjesečna isplata)<span></span>@if($_m_p_i === count($data['material_perks']) - 1 && empty($data['custom_material_perks'])). @endif
                                </li>
                            @endif
                            @if($_material_perk == 'transport_subvention')
                                <li>
                                    naknadu troškova prijevoza na posao i s posla mjesnim javnim prijevozom
                                    @if($data['material_perk_transport_subvention']['type'] === 'description')
                                        čiji bruto iznos je {!! $data['material_perk_transport_subvention']['value'] !!}
                                    @else
                                        u iznosu od {!! StringHelper::amountToText($data['material_perk_transport_subvention']['value']) !!} bruto
                                    @endif
                                    (mjesečna isplata)<span></span>@if($_m_p_i === count($data['material_perks']) - 1 && empty($data['custom_material_perks'])). @endif
                                </li>
                            @endif
                            @if($_material_perk == 'transport_subvention_intercity')
                                <li>
                                    naknadu troškova prijevoza na posao i s posla međumjesnim javnim prijevozom
                                    @if($data['material_perk_transport_subvention_intercity']['type'] === 'description')
                                        čiji bruto iznos je {!! $data['material_perk_transport_subvention_intercity']['value'] !!}
                                    @else
                                        u iznosu od {!! StringHelper::amountToText($data['material_perk_transport_subvention_intercity']['value']) !!} bruto
                                    @endif
                                    (mjesečna isplata)<span></span>@if($_m_p_i === count($data['material_perks']) - 1 && empty($data['custom_material_perks'])). @endif
                                </li>
                            @endif
                        @endforeach
                    @endif

                    @if(!empty($data['custom_material_perks']))
                        @foreach($data['custom_material_perks'] as $_c_m_p_i => $_custom_material_perk)

                            <li>
                                {!! !empty($_custom_material_perk['name']) ? $_custom_material_perk['name'] : $builder::$placeholder !!}
                                @if($_custom_material_perk['type'] === 'description')
                                    čiji bruto iznos je {!! !empty($_custom_material_perk['value']) ? $_custom_material_perk['value'] : $builder::$placeholder !!}
                                @else
                                    u iznosu od {!! !empty($_custom_material_perk['value']) ? StringHelper::amountToText($_custom_material_perk['value']) : ($builder::$placeholder . "EUR") !!} bruto
                                @endif
                                {{ "(" . $_custom_material_perk['frequency'] . ")" . ($_c_m_p_i === count($data['custom_material_perks'])-1 ? "." : null) }}
                            </li>

                            @if($_custom_material_perk['frequency'] == 'mjesečna isplata')
                                @php $contains_monthly_custom_material_perk = true; @endphp
                            @else
                                @php $contains_non_monthly_custom_material_perk = true; @endphp
                            @endif
                        @endforeach
                    @endif
                </ul>
            @endif


            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Poslodavac se obvezuje Radniku isplaćivati osnovnu plaću, dodatke odnosno povećanu plaću, ostale primitke za obavljeni rad i naknadu plaće jednom mjesečno
                i to najkasnije do @if($data['salary_day'] === 'custom') {!! $data['salary_day_custom'] !!}. dana tekućeg mjeseca za prethodni mjesec. @else 15. dana tekućeg mjeseca za prethodni mjesec. @endif
            </p>

            @if($contains_monthly_custom_material_perk || (!empty($data['material_perks']) && count(array_intersect(['transport_subvention', 'transport_subvention_intercity', 'meal_subvention'], $data['material_perks']))))
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Primitke na temelju radnog odnosa iz članka <span class="article-reference">{{ $custom_material_perk_article_index }}</span> ovog Ugovora koji se Radniku isplaćuju mjesečno Poslodavac se obvezuje Radniku isplatiti s
                    plaćom za mjesec u kojem je Radnik ostvario pravo na određeni primitak.
                </p>
            @endif

            @if($contains_non_monthly_custom_material_perk || (!empty($data['material_perks']) && count(array_intersect(['christmas', 'easter', 'child_gift', 'vacation_subvention'], $data['material_perks']))))
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Primitke na temelju radnog odnosa iz članka <span class="article-reference">{{ $custom_material_perk_article_index }}</span> ovog Ugovora koji se isplaćuju godišnje ili jednokratno Poslodavac se obvezuje Radniku isplatiti najkasnije
                    do kraja kalendarske godine u kojoj je Radnik ostvario pravo na određeni primitak.
                </p>
            @endif
        @endif

    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(10, true) }}. Prestanak radnog odnosa
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ovaj Ugovor može prestati u slučajevima i na način predviđen važećim Zakonom o radu.
        </p>

        @if($data['has_work_regulations'] && (!isset($data['notice_management_type']) || $data['notice_management_type'] === 'defined_in_work_regulations' || ($data['notice_management_type'] === 'defined_in_collective_agreement' && !$data['has_collective_agreement'])))
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Na sva pitanja vezana za postupak u slučaju otkazivanja ovog Ugovora te o otkaznim rokovima kojih se mora pridržavati Radnik odnosno Poslodavac primjenjuju se odgovarajuće odredbe Pravilnika o radu Poslodavca.
            </p>
        @elseif($data['has_collective_agreement'] && (!isset($data['notice_management_type']) || $data['notice_management_type'] === 'defined_in_collective_agreement' || ($data['notice_management_type'] === 'defined_in_work_regulations' && !$data['has_work_regulations'])))
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Na sva pitanja vezana za postupak u slučaju otkazivanja ovog Ugovora te o otkaznim rokovima kojih se mora pridržavati Radnik odnosno Poslodavac primjenjuju se odgovarajuće odredbe Kolektivnog ugovora.
            </p>
        @elseif($data['notice_management_type'] === 'custom')
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Ako Radnik redovito otkaže ovaj Ugovor, otkazni rok iznosi
                {!! $data['custom_notice_period_1'] !!}.
                Ako Poslodavac otkaže Ugovor Radniku u slučaju prestanka potrebe za obavljanjem poslova Radnog mjesta zbog gospodarskih, tehnoloških ili organizacijskih razloga (poslovno uvjetovani otkaz), otkazni rok iznosi
                {!! $data['custom_notice_period_2'] !!}.
                Ako Poslodavac otkaže Ugovor Radniku u slučaju da Radnik nije u mogućnosti uredno izvršavati svoje obveze iz Radnog odnosa zbog određenih trajnih osobina ili sposobnosti (osobno uvjetovani otkaz), otkazni rok iznosi
                {!! $data['custom_notice_period_3'] !!}.
                Ako Poslodavac otkaže Ugovor Radniku u slučaju da Radnik krši obveze iz Radnog odnosa (otkaz uvjetovan skrivljenim ponašanjem radnika), otkazni rok iznosi
                {!! $data['custom_notice_period_4'] !!}.
                @if($data['probation_period'])
                    Ako Poslodavac otkaže Ugovor Radniku u slučaju da Radnik nije zadovoljio na Probnom radu (otkaz zbog nezadovoljavanja na probnom radu) ili ako Radnik otkaže Ugovor tijekom Probnog rada, otkazni rok iznosi
                    {!! $data['custom_notice_period_5'] !!}.
                @endif
                Na sva pitanja vezana za postupak otkazivanja ovog Ugovora primjenjuju se odgovarajuće odredbe važećeg Zakona o radu.
            </p>
        @else
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Na sva pitanja vezana za postupak u slučaju otkazivanja ovog Ugovora te o otkaznim rokovima kojih se mora pridržavati Radnik odnosno Poslodavac primjenjuju se odgovarajuće odredbe važećeg Zakona o radu.
            </p>
        @endif
    </div>
</div>

@if($data['is_confidential'])
    <div class="editable-segment" data-type="article">
        <p class="article-header">
            Članak {{ $builder->getArticleIndex(11, true) }}. Obveza čuvanja poslovne tajne
        </p>
        <div class="article-body">
            @php $p_index = 1; @endphp
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Svu poslovnu dokumentaciju, podatke (uključujući, između ostalih, i sve podatke koji se mogu smatrati osobnim podacima sukladno
                Uredbi (EU) 2016/679 Europskog parlamenta i Vijeća od 27. travnja 2016. o zaštiti pojedinaca u vezi s obradom osobnih podataka
                i o slobodnom kretanju takvih podataka te o stavljanju izvan snage Direktive 95/46/EZ; u daljnjem tekstu: GDPR),
                informacije i spoznaje o poslovanju Poslodavca i njegovih poslovnih partnera, korisnika i klijenata, kao i drugih radnika,
                rukovodećeg osoblja i vanjskih suradnika koje dozna za trajanja ovog Ugovora Radnik je obvezan čuvati kao poslovnu tajnu
                za vrijeme i nakon prestanka radnog odnosa na temelju ovog Ugovora kod Poslodavca, bez vremenskog ograničenja.
            </p>
        </div>
    </div>
@else
    @php $builder->skipped_articles[] = 11; @endphp
@endif

@if($data['regulate_competition'])
    <div class="editable-segment" data-type="article">
        <p class="article-header">
            Članak {{ $builder->getArticleIndex(12, true) }}. Ugovorna zabrana natjecanja
        </p>
        <div class="article-body">
            @php $p_index = 1; @endphp
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Radnik prima na znanje i suglasan je s time da se, u slučaju prestanka ovog Ugovora, u razdoblju od
                {!! $data['regulate_competition_period'] !!} od dana prestanka Radnog odnosa ne smije zaposliti kod druge osobe koja je u tržišnom natjecanju s Poslodavcem
                te da ne smije za svoj račun ili za račun treće osobe sklapati poslove kojima se natječe s Poslodavcem
                (u daljnjem tekstu: Ugovorna zabrana natjecanja). Radnik prima na znanje i suglasan je s time da na temelju Ugovorne zabrane natjecanja,
                između ostalog, ne smije sa strankama ili partnerima Poslodavca ili s bilo kojom drugom osobom s kojom je Radnik inicijalno
                stupio u kontakt na radu ili u vezi s radom kod Poslodavca sklopiti ugovor o radu ili neki drugi pravni posao kojim bi se natjecao s Poslodavcem.
            </p>
            @if($data['regulate_competition_has_compensation'])
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    Poslodavac se obvezuje za vrijeme trajanja Ugovorne zabrane natjecanja Radniku isplaćivati naknadu
                    @if($data['regulate_competition_compensation'] == 'half_average_pay_for_last_three_months')
                        u iznosu polovice prosječne plaće isplaćene Radniku u tri mjeseca prije prestanka ovog Ugovora.
                    @elseif($data['regulate_competition_compensation'] == 'average_pay_for_last_three_months')
                        u iznosu prosječne plaće isplaćene radniku u tri mjeseca prije prestanka ovog Ugovora.
                    @elseif($data['regulate_competition_compensation'] == 'custom')
                        u iznosu od {!! StringHelper::amountToText($data['regulate_competition_compensation_custom']) !!} bruto.
                    @endif
                    Poslodavac se obvezuje Radniku isplaćivati navedenu naknadu jednom mjesečno i to najkasnije do 15. dana tekućeg mjeseca za prethodni mjesec.
                </p>
            @endif
            @if($data['regulate_competition_has_penalty'])
                <p>
                    {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                    U slučaju nepoštivanja Ugovorne zabrane natjecanja Radnik se obvezuje Poslodavcu platiti ugovornu kaznu
                    @if($data['regulate_competition_penalty'] == 'total_gross_income_for_12_months')
                        u iznosu 12 bruto plaća isplaćenih Radniku u posljednjih 12 mjeseci prije prestanka ovog Ugovora<span></span>@if(!$data['regulate_competition_has_compensation']). @endif
                    @elseif($data['regulate_competition_penalty'] == 'custom')
                        u iznosu od {!! StringHelper::amountToText($data['regulate_competition_penalty_custom']) !!} bruto<span></span>@if(!$data['regulate_competition_has_compensation']). @endif
                    @endif
                    @if($data['regulate_competition_has_compensation'])
                        te vratiti naknadu koju je Poslodavac Radniku isplatio na temelju prethodnog stavka ovog članka Ugovora.
                    @endif
                    Radnik se obvezuje Poslodavcu platiti ugovornu kaznu
                    @if($data['regulate_competition_has_compensation'])
                        i izvršiti povrat naknade
                    @endif
                    u roku od 15 dana od dostave pisanog poziva na plaćanje od strane Poslodavca.
                </p>
            @endif
        </div>
    </div>
@else
    @php $builder->skipped_articles[] = 12; @endphp
@endif

@if($data['transfer_intellectual_rights_to_employer'])
    <div class="editable-segment" data-type="article">
        <p class="article-header">
            Članak {{ $builder->getArticleIndex(13, true) }}. Prava intelektualnog vlasništva
        </p>
        <div class="article-body">
            @php $p_index = 1; @endphp
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Radnik prima na znanje i suglasan je s time da potpisom ovog Ugovora na Poslodavca prenosi autorsko pravo i sva druga prava intelektualnog vlasništva na svim predmetima koji se mogu zaštititi pravima intelektualnog vlasništva poput, između ostalih, autorskih djela, izuma, industrijskih dizajna, fizičkih proizvoda, ideja, postupaka, poboljšanja, koncepata, planova, programa, aplikacija i slično (u daljnjem tekstu: Intelektualna tvorevina), koju Radnik stvori za vrijeme trajanja ovog Ugovora izvršavajući svoje obveze i/ili po uputama Poslodavca tijekom radnog vremena i/ili izvan njega, s time da se prava prenose u najvećem mogućem opsegu u kojem je to dopušteno pozitivnim propisima primjenjivima u svakom pojedinom pravnom poretku na svijetu u kojem Poslodavac odluči iskorištavati Intelektualnu tvorevinu (u daljnjem tekstu: Pravni poredak). Radnik prima na znanje i suglasan je s time da Poslodavac za prethodno opisani prijenos prava intelektualnog vlasništva nema obvezu plaćanja bilo kakve naknade Radniku, osim ako je obveza isplate naknade izričito propisana propisom važećim u Pravnom poretku. Radnik potpisom ovog Ugovora daje pisanu suglasnost Poslodavcu da tako stečena prava intelektualnog vlasništva bez ograničenja prenosi dalje na treće osobe i to bez obveze plaćanja bilo kakve naknade Radniku, osim ako je obveza isplate naknade izričito propisana propisom važećim u Pravnom poretku.
            </p>
            <p>
                {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
                Ako vezano za autorsko pravo i/ili bilo koje drugo pravo intelektualnog vlasništva na Intelektualnoj tvorevini pozitivni propisi primjenjivi u Pravnom poretku ne dopuštaju prijenos prava s Radnika na Poslodavca u cijelosti i/ili djelomično, Radnik prima na znanje i suglasan je s time da potpisom ovog Ugovora za Poslodavca osniva isključivo te sadržajno, vremenski i prostorno neograničeno pravo iskorištavanja Intelektualne tvorevine i to na svaki sada i/ili u budućnosti mogući način (u daljnjem tekstu: Licencija). Radnik prima na znanje i suglasan je s time da Poslodavac za Licenciju nema obvezu plaćanja bilo kakve naknade Radniku, osim ako je obveza isplate naknade izričito propisana propisom važećim u Pravnom poretku. Radnik potpisom ovog Ugovora daje pisanu suglasnost Poslodavcu da tako stečenu Licenciju bez ograničenja prenosi dalje na treće osobe odnosno da na temelju tako stečene Licencije za treće osobe osniva daljnja prava iskorištavanja i to bez obveze plaćanja bilo kakve naknade Radniku, osim ako je obveza isplate naknade izričito propisana propisom važećim u Pravnom poretku.
            </p>
        </div>
    </div>
@else
    @php $builder->skipped_articles[] = 13; @endphp
@endif


<div class="editable-segment" data-section="additional_provisions" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex(14, true) }}. Zaštita zdravlja i sigurnosti na radu
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Poslodavac se obvezuje Radniku osigurati uvjete rada primjerene standardima sigurnosti, kao i sva potrebna zaštitna sredstva za rad u uvjetima rada koji postoje na poslovima koje Radnik obavlja. Poslodavac se obvezuje poštivati sve važeće propise iz područja zaštite na radu.
        </p>

        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Radnik se obvezuje pridržavati se propisanih i od Poslodavca određenih mjera sigurnosti na radu. Radnik prima na znanje i suglasan je s time da Poslodavac ne odgovara za štetu koju Radnik pretrpi na radu i u vezi s radom, ako se nije pridržavao propisanih i od Poslodavca određenih mjera sigurnosti na radu.
        </p>

        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Radnik potpisom ovog Ugovora potvrđuje da mu je Poslodavac na prikladan način učinio dostupnim pisane upute i propise o sigurnosti i zaštiti zdravlja na radu.
        </p>
    </div>
</div>

@php $dynamic_index = 15; @endphp

@if(!empty($data['custom_provisions']))
    @foreach($data['custom_provisions'] as $_i => $_custom_provision)
        @if(!empty(trim($_custom_provision['provision'])))
            <div class="editable-segment" data-type="article">
                <p class="article-header">
                    Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. {{ $_custom_provision['title'] }}
                </p>
                <div class="article-body">
                    @php
                        $p_index = 1;

                        $_custom_provision_content = App\Helpers\StringHelper::wordpressContent($_custom_provision['provision']);

                        foreach(explode("<p>", $_custom_provision_content) as $_p) {
                            if(!empty(trim($_p))){
                                echo "<p> ".$builder->getCurrentArticleIndex(). "." .$p_index++.". $_p";
                            }
                        }
                    @endphp
                </div>
            </div>
        @endif
    @endforeach
@endif

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Ništetnost
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ako se bilo koja odredba ovog Ugovora pokaže ništetnom, ostale odredbe ovog Ugovora u cijelosti ostaju na snazi. U slučaju ništetnosti jedne ili više odredaba ovog Ugovora, Ugovorne stranke se obvezuju odmah pristupiti zamjeni ništetnih odredaba drugima, vodeći pri tome računa da se izmijenjenim odredbama postigne isti stupanj zadovoljenja interesa Ugovornih stranaka, ali na način koji je dopušten.
        </p>
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Izmjene ugovora
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da bilo kakva izmjena, dopuna ili dodatak ovom Ugovoru moraju biti sastavljeni u pisanom obliku, valjano potpisani i odobreni od obiju Ugovornih stranaka, a eventualni usmeni dogovori moraju biti pisano potvrđeni.
        </p>
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Mjerodavno pravo
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da se na sva pitanja koja proizlaze iz ili u vezi s ovim Ugovorom  koja nisu izrijekom uređena u njemu primjenjuju odgovarajući propisi na snazi u Republici Hrvatskoj.
        </p>
    </div>
</div>

<div class="editable-segment" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Rješavanje sporova
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ugovorne stranke suglasno utvrđuju da će sve sporove koji nastanu iz ili u vezi s ovim Ugovorom koje ne mogu riješiti mirnim putem rješavati stvarno i mjesno nadležni sud.
        </p>
    </div>
</div>

<div class="editable-segment" data-section="final_provisions" data-type="article">
    <p class="article-header">
        Članak {{ $builder->getArticleIndex($dynamic_index++, true) }}. Broj primjeraka ugovora
    </p>
    <div class="article-body">
        @php $p_index = 1; @endphp
        <p>
            {{ $builder->getCurrentArticleIndex() }}.{{ $p_index++ }}.
            Ovaj Ugovor sklapa se u {!! $data['contract_copy_count'] !!} {{ StringHelper::getPrimjerakaString($data['contract_copy_count']) }}, od kojih {!! $data['employer_contract_copy_count'] !!} zadržava Poslodavac, a {!! $data['employee_contract_copy_count'] !!} zadržava Radnik.
        </p>
    </div>
</div>

@include('layouts.tenant.document.partials.signatures', [
  'data' => $data,
  'builder' => $builder,
  'dynamic_index' => $dynamic_index,
  'text' => 'Ugovorne stranke su suglasne da je u odredbama ovog Ugovora sadržana njihova prava i stvarna volja te ga u znak prihvata prava i obveza koje iz Ugovora proizlaze vlastoručno potpisuju.',
  'left_parties' => $builder->getParties()->where('side', 'left')->values()->toArray(),
  'right_parties' => $builder->getParties()->where('side', 'right')->values()->toArray(),
  'default_party_label_left' => 'Poslodavac',
  'default_party_label_right' => 'Radnik',
])
