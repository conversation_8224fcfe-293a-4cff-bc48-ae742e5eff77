/*!
 * Start Bootstrap - Landing Page v5.0.0 (https://startbootstrap.com/template-overviews/landing-page)
 * Copyright 2013-2018 Start Bootstrap
 * Licensed under MIT (https://github.com/BlackrockDigital/startbootstrap-landing-page/blob/master/LICENSE)
 */

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: sans-serif;
    font-weight: 700;
}

.btn-success, .btn-danger, .btn-info {
    color: #fff !important;
}

@media (min-width: 992px) {
    .stretch-nav-full-lg {
        width: 102%;
    }
}

header.masthead {
    position: relative;
    background-color: #506d86;
    background-size: cover;
    padding-top: 6rem;
    padding-bottom: 4rem;
}

header.masthead .overlay {
    position: absolute;
    background-color: #212529;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    opacity: 0.3;
}

header.masthead h1 {
    font-size: 2rem;
}

@media (min-width: 768px) {
    header.masthead {
        padding-top: 6rem;
        padding-bottom: 4rem;
    }

    header.masthead h1 {
        font-size: 3rem;
    }
}

.tooltip-inner {
    max-width: 400px;
}

@media (max-width: 768px) {
    .tooltip-inner {
        max-width: 300px;
    }
}

.showcase .showcase-text {
    padding: 3rem;
}

.showcase .showcase-img {
    min-height: 30rem;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

@media (min-width: 768px) {
    .showcase .showcase-text {
        padding: 7rem;
    }
}

.features-icons {
    padding-top: 4rem;
    padding-bottom: 7rem;
}

.features-icons .features-icons-item {
    max-width: 20rem;
}

.features-icons .features-icons-item .features-icons-icon {
    height: 7rem;
}

.features-icons .features-icons-item .features-icons-icon i {
    font-size: 4.5rem;
}


.testimonials {
    padding-top: 7rem;
    padding-bottom: 7rem;
}

.testimonials .testimonial-item {
    max-width: 18rem;
}

.testimonials .testimonial-item img {
    max-width: 12rem;
    -webkit-box-shadow: 0px 5px 5px 0px #adb5bd;
    box-shadow: 0px 5px 5px 0px #adb5bd;
}

.call-to-action {
    position: relative;
    background-color: #343a40;
    background-size: cover;
    padding-top: 7rem;
    padding-bottom: 7rem;
}

.call-to-action .overlay {
    position: absolute;
    background-color: #212529;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    opacity: 0.3;
}


footer.footer a {
    color: rgba(0, 0, 0, .9);
}

.margin-top {
    margin-top: 30px;
}


/* Sticky footer CSS */
html,
body {
    margin: 0;
    padding: 0;
    height: 100%;
}

#layout-container {
    min-height: 100%;
    position: relative;
}

#layout-content {
    padding: 10px;
    padding-bottom: 150px; /* Height of the footer */
    padding-top: 70px;
}

footer.footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 150px; /* Height of the footer */
    line-height: 50px;

}

/* Set color schema to btn-info */
.btn-link {
    color: #000000 !important;
}

.dropdown-item:active {
    background-color: #ffffff !important;
    color: #212529 !important;
}

/* Datatables customisations */

table.dataTable.dtr-inline.collapsed>tbody>tr>td.dtr-control, table.dataTable.dtr-inline.collapsed>tbody>tr>th.dtr-control {
    vertical-align: middle;
    padding-left: 35px;
}

@media (max-width: 1024px) {
    .hide-on-mobile{
        display:none;
    }
}

table.dataTable.dtr-inline.collapsed > tbody > tr > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr > th:first-child:before {
    background-color: #1a3b5d !important;
}

table.dataTable > tbody > tr.child ul li {
    border-bottom: none !important;
}

table.dataTable tbody tr.selected>*, table.dataTable.table-striped>tbody>tr.odd.selected>* {
    box-shadow: inset 0 0 0 9999px #1a3b5d;
    color: white;
}

@media (min-width: 1024px) {
    .dataTables_filter{
        display: inline-block;
        float:right;
    }

    .dataTables_length{
        display: inline-block;
        float:left;
    }
}


.page-item.active .page-link {
    background-color: #f2f2f2;
    border-color: #e3e3e3;
    color: #000000;
}

.page-link:focus{
    outline-style: none;
    box-shadow: none;
    border-color: transparent;
}
.page-link {
    color: #000000;
    border:none;
}

.page-link:hover{
    color: #525252;
    background-color: #f2f2f2;
    border-color: #e3e3e3;
}

table.table-bordered.dataTable {
    border-top-width: 0;
}

.table-bordered thead td, .table-bordered thead th {
    border-bottom-width:1px;
}

table.dataTable thead > tr > th.sorting::after, table.dataTable thead > tr > th.sorting_asc::after, table.dataTable thead > tr > th.sorting_desc::after, table.dataTable thead > tr > th.sorting_asc_disabled::after, table.dataTable thead > tr > th.sorting_desc_disabled::after, table.dataTable thead > tr > td.sorting::after, table.dataTable thead > tr > td.sorting_asc::after, table.dataTable thead > tr > td.sorting_desc::after, table.dataTable thead > tr > td.sorting_asc_disabled::after, table.dataTable thead > tr > td.sorting_desc_disabled::after {
    content: "\25BC"!important;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before, table.dataTable thead>tr>th.sorting_desc:before, table.dataTable thead>tr>th.sorting_asc_disabled:before, table.dataTable thead>tr>th.sorting_desc_disabled:before, table.dataTable thead>tr>td.sorting:before, table.dataTable thead>tr>td.sorting_asc:before, table.dataTable thead>tr>td.sorting_desc:before, table.dataTable thead>tr>td.sorting_asc_disabled:before, table.dataTable thead>tr>td.sorting_desc_disabled:before {
    content: "\25B2"!important;
}

table.dataTable>tbody>tr.selected>td.select-checkbox:after, table.dataTable>tbody>tr.selected>th.select-checkbox:after {
    content: "\2713 "!important;
}

/* Custom form components CSS */
.radio_input_text {
    border-radius: 5px;
    border: 1px solid #cccccc;
    height: 38px;
    padding-left: 10px;
}

.checkbox_input_text {
    border-radius: 5px;
    border: 1px solid #cccccc;
}


/* Blog */
.alignnone {
    margin: 5px 20px 20px 0;
}

.aligncenter,
div.aligncenter {
    display: block;
    margin: 5px auto 5px auto;
}

.alignright {
    float: right;
    margin: 5px 0 20px 20px;
}

.alignleft {
    float: left;
    margin: 5px 20px 20px 0;
}

a img.alignright {
    float: right;
    margin: 5px 0 20px 20px;
}

a img.alignnone {
    margin: 5px 20px 20px 0;
}

a img.alignleft {
    float: left;
    margin: 5px 20px 20px 0;
}

a img.aligncenter {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.wp-caption {
    background: #fff;
    border: 1px solid #f0f0f0;
    max-width: 96%; /* Image does not overflow the content area */
    padding: 5px 3px 10px;
    text-align: center;
}

.wp-caption.alignnone {
    margin: 5px 20px 20px 0;
}

.wp-caption.alignleft {
    margin: 5px 20px 20px 0;
}

.wp-caption.alignright {
    margin: 5px 0 20px 20px;
}

.wp-caption img {
    border: 0 none;
    height: auto;
    margin: 0;
    max-width: 98.5%;
    padding: 0;
    width: auto;
}

.wp-caption p.wp-caption-text {
    font-size: 11px;
    line-height: 17px;
    margin: 0;
    padding: 0 4px 5px;
}

/* Text meant only for screen readers. */
.screen-reader-text {
    border: 0;
    clip: rect(1px, 1px, 1px, 1px);
    clip-path: inset(50%);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute !important;
    width: 1px;
    word-wrap: normal !important; /* Many screen reader and browser combinations announce broken words as they would appear visually. */
}

.screen-reader-text:focus {
    background-color: #eee;
    clip: auto !important;
    clip-path: none;
    color: #444;
    display: block;
    font-size: 1em;
    height: auto;
    left: 5px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 5px;
    width: auto;
    z-index: 100000;
}

.article_item {
    border: 1px solid rgba(0, 0, 0, .125);
    border-radius: 8px;
}

/* Custom */
a, a:hover {
    color: #000000;
}

.sticky-offset {
    top: 75px;
}

.sticky-offset-100 {
    top: 100px;
}

.sticky-offset-125 {
    top: 125px;
}

.headerbox {
    color: white;
    text-align: center;
}

.lead {
    font-size: 1.20rem;
    letter-spacing: 0.2px;
}

@font-face {
    font-family: MyAndroid;
    src: url('/fonts/android.ttf');
}

.form-control::-webkit-input-placeholder {
    color: #9c9c9c;
}

.form-control::-moz-placeholder {
    color: #9c9c9c;
}

.form-control:-ms-input-placeholder {
    color: #9c9c9c;
}

/* Make Select2 boxes match Bootstrap3 heights: */
.select2-selection__rendered {
    line-height: 40px !important;
    text-align: left;
}

.select2-selection, .select2-selection__arrow {
    height: 40px !important;
}

.document-search-container .select2-selection__rendered {
    line-height: 48px !important;
    text-align: left;
    font-size: 20px;
}

.document-search-container .select2-selection, .document-search-container .select2-selection__arrow {
    height: 48px !important;
}

/* Wizard Navbar */
#sectionNavbar ul {
    width: 100%;
    font-size: .8rem;
    justify-content: space-evenly;
    display: flex;
    margin: auto;
}

#sectionNavbar .active, #navbarNav .active {
    font-weight: bold;
}

#navbarNav {
    border-top: 1px solid #f0ecec;
    margin-top: 10px;
}

.navbar-brand{
    padding-top: .25rem;
    padding-bottom: 0;
}

@media (max-width: 768px) {
    #sectionNavbar ul li {
        width: 100% !important;
    }

    .dot {
        display: none;
    }

    .dot:before {
        content: "" !important;
    }

    .mobile-content {
        display: none;
    }

    .mobile-navbar-brand {
        display: inline-block;
        padding-top: .3125rem;
        padding-bottom: .3125rem;
        margin-right: 1rem;
        font-size: 1.25rem;
        width: 65%;
        line-height: inherit;
        color: rgba(0, 0, 0, .9);
    }

    .g-recaptcha {
        transform: scale(0.8);
        transform-origin: 0 0;
    }
}

.dot:before {
    content: "\21E8";
    margin-right: 2px;
    color: #343a40;
}

.document-search-container .select2-selection__placeholder {
    color: #444 !important;
}

.table td.fit,
.table th.fit {
    white-space: nowrap;
    width: 1%;
}

@media (min-width: 1024px) {
    .navbar .divider-vertical {
        height: 40px;
        margin: 0 10px;
    }
}


@media (min-width: 768px) {
    .visible-xs {
        display: none !important;
    }
}

a.disabled {
    pointer-events: none;

}

.dropdown-item.disabled, .dropdown-item:disabled {
    color: #6c757d78;
    background-color: transparent;
}

.btn-default {
    background: white;
    color: black !important;
    border: 1px solid #ced4da !important;
}

.fancybox-caption__body {
    padding-bottom: 2%;
}

.has-error input:not([type="radio"]):not([type="checkbox"]), .has-error textarea {
    box-shadow: 0 0 5px red;
}

.has-warning {
    box-shadow: 0 0 5px orange;
    border-color: transparent;
}

.button-helper {
    margin-top: 10px;
    font-size: 0.9em;
}

.fancybox-container {
    z-index: 100000000;
}

.bold {
    font-weight: bold;
}

.document-details {
    border-top: 1px solid #cacaca;
    margin-top: 10px;
    padding-top: 10px;
    font-size: .80rem;
}

@media (min-width: 1024px) {

    .example-intro p{
        text-align: justify;
    }

    .site-content p{
        text-align: justify;
    }

    .site-content li{
        text-align: justify;
    }

    .site-content div{
        text-align: justify;
    }

    blockquote{
        display: block;
        margin-block-start: 1em;
        margin-block-end: 1em;
        margin-inline-start: 40px;
        margin-inline-end: 40px;
    }
}

@media (max-width: 768px) {
    .footer-bullet{
        display: none;
    }
    .footer-link{
        display: block!important;
        margin-right: 0.5rem!important;
    }

    .fancybox-caption__body{
        text-align: left;
    }
}

.tooltip-inner{
    text-align: left;
}

.spaced-list li{
    padding: 5px 0;
}

h1, .h1 {
    font-size: 2rem;
}

h2, .h2 {
    font-size: 1.75rem;
}

h3, .h3 {
    font-size: 1.5rem;
}

h4, .h4 {
    font-size: 1.25rem;
}

h5, .h5 {
    font-size: 1rem;
}

h6, .h6 {
    font-size: 1rem;
}

.card{
    border-radius: 8px;
}

@media(max-width: 768px){
    .site-content img{
        float: none!important;
        display: block;
        margin-left: auto;
        margin-right: auto;
    }

    .text-small-left{
        text-align: left!important;
    }
}

.support-widget{
    margin-top: 0;
    margin-right: 0;
    margin-bottom: 0;
    padding: 0;
    border: 0;
    background: #14a2b8;
    overflow: hidden;
    position: fixed;
    z-index: 16000002;
    width: 146px;
    height: 28px;
    right: 10px;
    bottom: 0;
    text-align: center;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

.support-widget a{
    color: #FFFFFF;
    font-weight: bold;
    font-size:13px;
}

.support-widget a:hover{
    text-decoration: none;
}

@media (max-width: 1024px) {
    .support-widget{
        display: none;
    }
}

.text-input-error{
    color: red;
}

.transform-lowercase{
    text-transform: lowercase;
}

::-webkit-input-placeholder { /* WebKit browsers */
    text-transform: none;
}
:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
    text-transform: none;
}
::-moz-placeholder { /* Mozilla Firefox 19+ */
    text-transform: none;
}
:-ms-input-placeholder { /* Internet Explorer 10+ */
    text-transform: none;
}
::placeholder { /* Recent browsers */
    text-transform: none;
}

input[data-datepicker] {
    background-color: #FFFFFF!important;
    cursor: pointer;
}

#overlay {
    position: fixed; /* Sit on top of the page content */
    display: none; /* Hidden by default */
    width: 100%; /* Full width (cover the whole page) */
    height: 100%; /* Full height (cover the whole page) */
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0,0,0,0.5); /* Black background with opacity */
    z-index: 2; /* Specify a stack order in case you're using a different order for other elements */
    cursor: pointer; /* Add a pointer on hover */
}

.rating {
    display: flex;
    flex-direction: row-reverse;
    justify-content: center
}

.rating>input {
    display: none
}

.rating>label {
    position: relative;
    width: 1em;
    font-size: 30px;
    font-weight: 300;
    color: #FFD600;
    cursor: pointer
}

.rating>label::before {
    content: "\2605";
    position: absolute;
    opacity: 0
}

.rating>label:hover:before,
.rating>label:hover~label:before {
    opacity: 1 !important
}

.rating>input:checked~label:before {
    opacity: 1
}

.rating:hover>input:checked~label:before {
    opacity: 0.4
}

.site-content .alert-danger{
    color: black;
    background-color: #e96e7a30;
    border-color: #f5c6cb;
}

.btn-group-xs > .btn, .btn-xs {
    padding: .25rem .4rem;
    font-size: .700rem;
    line-height: .5;
    border-radius: .2rem;
}

@media (min-width: 1024px) {
    #modal-import-documents .table-responsive{
        overflow-y:auto;
        max-height: 500px;
    }
}

#document-drafts-table .dropdown, #documents-table .dropdown {
    min-width: 120px;
}

#alert-widget{
    margin-top: 0;
    margin-right: 0;
    margin-bottom: 0;
    padding: 0;
    border: 0;
    overflow: hidden;
    position: fixed;
    z-index: 16000002;
    width: 360px;
    height: 63px;
    right: 10px;
    bottom: 0;
    text-align: center;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    cursor: default;
}

.full-width{
    width: 100%;
}

.custom-select{
    border-radius: 0.25rem 0 0 0.25rem;
}

.custom-select:focus{
    outline-style: none;
    box-shadow: none;
    border-color: #ced4da;
}

.select2-search__field:focus {
    outline: none !important;
}

#search-document-placeholder{
    padding-left: 15px;
}

#search-document{
    width:100%!important;
}

#search-document-container .select2-selection__arrow{
    display:none;
}

#search-document-container .select2, #search-document-container .select2-selection--single {
    width: 100% !important;
    height: 38px!important;
}

#search-document-container .select2-selection__rendered{
    line-height: 2.3!important;
}

#search-document-container .select2-selection__placeholder{
    display: block;
    line-height: 2.3;
}

#select2-search-document-container{
    padding-left: 15px;
}

#search-library input:focus {
    outline: none!important;
    border-color: #ced4da;
    -webkit-box-shadow: none;
    box-shadow: none;
}

#wizard-toggle {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}

#wizard-toggle:focus, #editor-toggle:focus {
    outline: none!important;
    border-color: #ced4da;
    -webkit-box-shadow: none;
    box-shadow: none;
}

#editor-toggle {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

.fading-content {
    position: relative;
    max-height: 100vh;
    overflow: hidden;
}

@media (max-width: 1024px) {
    .fading-content {
        position: relative;
        max-height: 50vh;
        overflow: hidden;
    }
}

.fadeout {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 300px;
    background: linear-gradient(to bottom, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%);
}

.signup-notice {
    text-align: center;
    padding: 20px 0;
    font-size: 18px;
}

.disable-select {
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none;
    -webkit-user-drag: none; /* Safari */
    -khtml-user-drag: none; /* Konqueror HTML */
    -moz-user-drag: none; /* Firefox */
    -ms-user-drag: none; /* Internet Explorer/Edge */
    user-drag: none;
}

.example-preview span{
    background-color: transparent!important;
}

.example-preview{
    pointer-events: none;
}

.export-segment{
    cursor: pointer;
    color: #1a3b5d!important;
}

#filter-example-results{
    font-size: .9rem;
}

.datatable-container {
    min-height: 200px;
    height: 55vh;
    padding: 5px;
}

/* This is to ensure the scrollbar only appears when necessary */
.datatable-container::-webkit-scrollbar {
    width: 0.5em;
}

.datatable-container::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

.datatable-container::-webkit-scrollbar-thumb {
    background-color: darkgrey;
    outline: 1px solid slategrey;
}

#search-articles-table tbody span{
    background-color: transparent!important;
}

#search-articles-table .details-control{
    cursor: pointer;
    color: #1a3b5d;
}

@media (min-width: 768px) {
    .navbar-expand-md .navbar-nav .nav-link {
        padding-right: 1rem;
        padding-left: 1rem;
    }


    .navbar-nav .dropdown:hover > .dropdown-menu {
        display: block;
        z-index: 1050;
    }

    .navbar-nav .dropdown-menu {
        margin-top: 0;
    }
}

.nav-link {
    padding: 0.5rem 1rem;
}


.alert-secondary {
    color: #383d41;
    background-color: #e2e3e563;
    border-color: #d6d8db;
}

#clear-query-input:focus{
    outline: none!important;
    -webkit-box-shadow: none;
    box-shadow: none;
}

#clear-query-input{
    font-size: .8rem;
}

/* Hide google reCaptcha badge */
.grecaptcha-badge { visibility: hidden !important; }

.example-intro p:last-of-type {
    margin-bottom: 0;
}

.prevent-select {
    -webkit-user-select: none; /* Safari */
    -ms-user-select: none; /* IE 10 and IE 11 */
    user-select: none; /* Standard syntax */
}

@media (max-width: 1024px) {
    #navbar-dropdown-menu {
        background-color: #343a40;
        color: white;
        border: none;
    }
    #navbar-dropdown-menu .dropdown-item {
        color: rgba(255,255,255,.5);
    }
    #navbar-dropdown-menu .dropdown-item:active {
        color: white!important;
        background-color: rgba(255, 255, 255, 0.15)!important;
    }
    #navbar-dropdown-menu .dropdown-item:focus, .dropdown-menu .dropdown-item:hover {
        background-color: rgba(255, 255, 255, 0.15);
    }

}

.badge-orange{
    color: #fff;
    background-color: orange;
}

.dirty-row{
    background: #ffeded;
}

.article-post-title{
    color: #aaa;
    font-size: .8rem;
}

#search-articles-table tr.even, #search-articles-table tr.odd{
    cursor: pointer;
}

#document-preview{
    pointer-events: none; /* Disables mouse events like clicking, hovering, etc. */
    user-select: none; /* Disables text selection for most browsers */
    -webkit-user-select: none; /* Disables text selection for Safari */
    -moz-user-select: none; /* Disables text selection for Firefox */
    -ms-user-select: none; /* Disables text selection for Internet Explorer/Edge */
}

.section-navbar-sticky {
    position: sticky;
    top: 40px;
    background-color: inherit;
    z-index: 2;
}

ol.footnotes {
    margin-left: 0;
    padding-left: 15px;
    list-style-type: decimal;
}

ol.footnotes li a {
    text-decoration: none;
    font-size: smaller;
    color: #666;
}

#pravomat-reviews-link {
    color: white;
}

@media (max-width: 480px) {
    .documentDropdown, .documentDraftDropdown {
        margin-top: -20px;
    }
}

.StripeElement {
    border: 1px solid #ced4da;
    padding: 0.475rem 0.75rem;
    border-radius: 0.25rem;
    background: #fff;
}

#documents-table th, #document-drafts-table th {
    font-weight: 600;
}

.reviews-star {
    color: #ffc83d;
}

@media (max-width: 992px) {
    #user-menu {
        border: 0;
        background: #343a40;
    }

    #user-menu .dropdown-item {
        color: rgba(255,255,255,.5);
    }

    #user-menu .dropdown-item:hover {
        color: white;
        background-color: rgba(255, 255, 255, 0.15);
    }
}

.btn-primary,
.bg-primary,
.text-primary {
    background-color: #1a3b5d !important;
    border-color: #1a3b5d !important;
    color: #fff !important; /* For buttons or text contrast */
}

.text-primary {
    color: #1a3b5d !important;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #1a3b5d;
    color: white;
}

.home-intro {
    font-size: 3rem!important;
}
