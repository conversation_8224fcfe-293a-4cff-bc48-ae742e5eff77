<?php

declare(strict_types=1);

use App\Http\Controllers\DebugController;
use App\Http\Controllers\Tenant\EditorController;
use App\Http\Controllers\Tenant\EmailController;
use App\Http\Controllers\Tenant\SignatureController;
use App\Http\Controllers\Tenant\RedirectController;
use App\Http\Controllers\Tenant\SearchController;
use App\Http\Controllers\Tenant\DocumentController;
use App\Http\Controllers\Tenant\SectionController;
use App\Http\Controllers\Tenant\UserController;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;
use App\Http\Controllers\Tenant\HomeController;
use App\Http\Controllers\Tenant\Auth\LoginController;
use App\Http\Controllers\Tenant\Auth\RegisterController;
use App\Http\Controllers\Tenant\Auth\ForgotPasswordController;
use App\Http\Controllers\Tenant\Auth\ResetPasswordController;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application. These
| routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->group(function () {

    // only allow in local environment
    Route::group(['middleware' => ['local']], function () {
        Route::any('/debug', [DebugController::class, 'index']);
    });

    // invalidate browser "back" cache
    Route::group(['middleware' => ['cache.headers:no_cache;no_store;must_revalidate;max_age=0']], function () {
        // Home route
        Route::get('/', [HomeController::class, 'index'])->name('tenant.home');

        // Document routes
        Route::post('/document/create', [DocumentController::class, 'create'])->name('tenant.document.create');

        // Other routes
        Route::get('/redirect/to', [RedirectController::class, 'to'])->name('redirect.to');
    });

    // Authenticated routes
    Route::group(['middleware' => ['auth']], function () {

        // Dashboard
        Route::get('/nadzorna-ploca', [DocumentController::class, 'index'])->name('tenant.dashboard');

        // Search routes
        Route::post('/template/search', [SearchController::class, 'templates'])->name('tenant.search.templates');

        Route::get('/user/documents', [DocumentController::class, 'dataTables'])->name('tenant.user.documents');
        Route::get('/user/drafts', [EditorController::class, 'draftsDataTables'])->name('tenant.user.drafts');

        // Tutorial routes
        Route::post('/user/wizard/tutorial', [SectionController::class, 'setWizardTutorialShown'])->name('tenant.wizard.tutorial');
        Route::post('/user/documents/tutorial', [UserController::class, 'setDocumentTutorialShown'])->name('documents.tutorial');
        Route::post('/user/editor/draft/tutorial', [UserController::class, 'setDraftTutorialShown'])->name('editor.draft.tutorial');
        Route::post('/user/editor/tutorial', [UserController::class, 'setEditorTutorialShown'])->name('editor.tutorial');

        // Editor routes
        Route::group(['middleware' => ['can:update,draft']], function () {
            Route::get('/uredivac/{draft}', [EditorController::class, 'edit'])->name('tenant.editor.draft.edit');
            Route::post('/editor/{draft}/save', [EditorController::class, 'save'])->name('tenant.editor.draft.save');
            Route::get('/uredivac/{draft}/izbrisi', [EditorController::class, 'delete'])->name('tenant.editor.draft.delete');
            Route::any('/editor/{draft}/comment', [EditorController::class, 'comment'])->name('tenant.editor.draft.comment');
            Route::any('/editor/{draft}/title', [EditorController::class, 'title'])->name('tenant.editor.draft.title');
            Route::get('/editor/{draft}/stream', [EditorController::class, 'stream'])->name('tenant.editor.draft.stream');
            Route::get('/uredivac/{draft}/dupliciraj', [EditorController::class, 'duplicate'])->name('tenant.editor.draft.duplicate');
            Route::get('/uredivac/dokument/{draft}/posalji', [EmailController::class, 'draft'])->name('tenant.editor.draft.email');
            Route::get('/uredivac/dokument/{draft}/prevedi', [EditorController::class, 'translate'])->name('tenant.editor.draft.translate');
            Route::post('/editor/{draft}/send', [EmailController::class, 'sendDraft'])->name('tenant.editor.draft.send');
        });

        Route::group(['middleware' => ['admin']], function () {
            Route::get('/uredivac/{draft}/dupliciraj/admin', [EditorController::class, 'duplicateAdmin'])->name('tenant.editor.draft.duplicate.admin');
            Route::get('/dokument/{document}/dupliciraj/admin', [DocumentController::class, 'duplicateAdmin'])->name('tenant.document.duplicate.admin');
            Route::get('/uredivac/{draft}/preuzmi/admin', [EditorController::class, 'downloadAdmin'])->name('tenant.editor.draft.download.admin');
        });

        Route::get('/editor/{post}/articles/search', [EditorController::class, 'articlesDataTables'])->name('tenant.editor.articles');
        Route::post('/editor/spellcheck', [EditorController::class, 'spellCheck'])->name('tenant.editor.spellcheck')->middleware('throttle:30,1');

    });

    // signed routes
    Route::group(['middleware' => ['signed']], function () {
        Route::get('/dokument/{document}/stream', [DocumentController::class, 'stream'])->name('tenant.document.stream');
        Route::post('/document/preview/html', [DocumentController::class, 'previewHtml'])->name('tenant.document.preview.html');
        Route::get('/dokument/{document}/pregled', [DocumentController::class, 'previewPdf'])->name('tenant.document.preview.image');
        Route::get('/dokument/{document}/preuzmi', [DocumentController::class, 'download'])->name('tenant.document.download');
        Route::get('/uredivac/{draft}/preuzmi', [EditorController::class, 'download'])->name('tenant.editor.draft.download');
        Route::get('/stranka/{party}/potpis', [SignatureController::class, 'show'])->name('tenant.signature.show');
        Route::get('/stranka/{party}/potpis/image', [SignatureController::class, 'image'])->name('tenant.signature.image');
        Route::post('/party/{party}/signature/save', [SignatureController::class, 'save'])->name('tenant.signature.save');
    });

    // document routes - restrict to document owners
    Route::group(['middleware' => ['auth', 'verified', 'can:update,document']], function () {
        Route::get('/dokument/{document}/izbrisi', [DocumentController::class, 'delete'])->name('tenant.document.delete');
        Route::get('/dokument/{document}/posalji', [EmailController::class, 'document'])->name('tenant.email.document');
        Route::get('/dokument/{document}/dupliciraj', [DocumentController::class, 'duplicate'])->name('tenant.document.duplicate');
        Route::get('/dokument/{document}/izvezi', [DocumentController::class, 'exportToEditor'])->name('tenant.document.exportToEditor');
        Route::get('/dokument/{document}/fork', [DocumentController::class, 'fork'])->name('tenant.document.fork');
        Route::post('/dokument/{document}/send', [EmailController::class, 'sendDocument'])->name('tenant.email.sendDocument');
        Route::get('/dokument/{document}/stranka/{party}/zatrazi-potpis', [SignatureController::class, 'request'])->name('tenant.signature.request');
        Route::post('/dokument/{document}/party/{party}/signature/request', [SignatureController::class, 'requestSend'])->name('tenant.signature.request.send');
        Route::post('/dokument/{document}/signatureRequest/{request}/revoke', [SignatureController::class, 'requestRevoke'])->name('tenant.signature.request.revoke');
        Route::any('/document/{document}/comment', [DocumentController::class, 'comment'])->name('tenant.document.comment');
        Route::get('/dokument/{document}/potpisi', [SignatureController::class, 'index'])->name('tenant.signature.index');
        Route::get('/dokument/{document}/stranka/{party}/zatrazi-potpis', [SignatureController::class, 'request'])->name('tenant.signature.request');
        Route::post('/dokument/{document}/party/{party}/signature/request', [SignatureController::class, 'requestSend'])->name('tenant.signature.request.send');
        Route::post('/dokument/{document}/signatureRequest/{request}/revoke', [SignatureController::class, 'requestRevoke'])->name('tenant.signature.request.revoke');

    });

    // Document section (wizard) routes
    Route::group(['middleware' => ['can:update,document']], function () {
        Route::get('/dokument/{document}/{section}', [SectionController::class, 'show'])->name('tenant.section.show');
        Route::post('/dokument/{document}/{section}', [SectionController::class, 'store'])->name('tenant.section.store');
    });

    // Throttled routes
    Route::group(['middleware' => ['throttle']], function () {
        // Authentication routes
        Route::get('/login', [LoginController::class, 'showLoginForm'])->name('tenant.login');
        Route::post('/login', [LoginController::class, 'login']);
        Route::post('/logout', [LoginController::class, 'logout'])->name('tenant.logout');

        // Registration routes
        Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('tenant.register');
        Route::post('/register', [RegisterController::class, 'register']);

        // Password reset routes
        Route::get('/password/reset', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('tenant.password.request');
        Route::post('/password/email', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('tenant.password.email');
        Route::get('/password/reset/{token}', [ResetPasswordController::class, 'showResetForm'])->name('tenant.password.reset');
        Route::post('/password/reset', [ResetPasswordController::class, 'reset'])->name('tenant.password.update');

        // OTP Verification Routes
        Route::get('/otp/verify', [LoginController::class, 'showOtpForm'])->name('tenant.otp.form');
        Route::post('/otp/verify', [LoginController::class, 'verifyOtp'])->name('tenant.otp.verify');
    });

    // javascript error reporting
    Route::post('/jserror', function () {

        if(app()->environment('production')) {
            return true; // disable error reporting on production
        }

        $errorData = request()->all();
        $error = json_encode($errorData);

        // List of predefined errors to ignore
        $ignored_errors = [
            "ReferenceError: Can't find variable: gmo",
        ];

        // Check if errorMsg matches any ignored error
        if (!empty($errorData['errorMsg'])) {
            foreach ($ignored_errors as $_ignored_error) {
                if (str_contains($errorData['errorMsg'], $_ignored_error)) {
                    return false;
                }
            }
        }

        // Check if the error originated from an external script
        $script_url = request()->get('scriptURL');
        $is_external_script = $script_url && !str($script_url)->contains(request()->getHost());
        $is_generic_error = !$script_url;

        // In production, ignore errors from crawlers and external scripts
        if (app()->environment('production')) {
            $cd = new \Jaybizzle\CrawlerDetect\CrawlerDetect();

            if ($cd->isCrawler() || $is_external_script || $is_generic_error) {
                return false;
            }
        }

        // Log or throw exception for other cases
        throw new \Exception("Error in JavaScript: $error");
    });

});
